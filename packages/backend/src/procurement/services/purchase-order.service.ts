import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { PinoLogger, InjectPinoLogger } from 'nestjs-pino';
import { PrismaService } from '../../prisma/prisma.service';
import { CreatePurchaseOrderDto } from '../dto/create-purchase-order.dto';
import { UpdatePurchaseOrderDto } from '../dto/update-purchase-order.dto';
import { PurchaseOrderQueryDto, PurchaseOrderStatusUpdateDto } from '../dto/purchase-order-query.dto';
import { NumberGeneratorService } from '../utils/number-generator.utils';
import { ProcurementValidationUtils } from '../utils/procurement-validation.utils';
import { TaxCalculationService } from './tax-calculation.service';
import { TaxConfigurationService } from './tax-configuration.service';
import { TaxType } from '../dto/tax-configuration.dto';
import { Prisma, PurchaseOrderStatus, GoodsReceiptStatus } from '@prisma/client';

@Injectable()
export class PurchaseOrderService {
  constructor(
    private prisma: PrismaService,
    private numberGeneratorService: NumberGeneratorService,
    private taxCalculationService: TaxCalculationService,
    private taxConfigurationService: TaxConfigurationService,
    @InjectPinoLogger(PurchaseOrderService.name)
    private readonly logger: PinoLogger,
  ) { }

  /**
   * Compute tax amount and total amount based on subtotal, discount, and tax options
   */
  private async computeTax(
    subtotal: Prisma.Decimal,
    orderDiscount: Prisma.Decimal,
    options: {
      auto: boolean;
      inclusive: boolean;
      taxOptions?: {
        taxType?: TaxType;
        exemptItems?: string[];
      };
      manualTaxAmount?: number;
    }
  ): Promise<{ taxAmount: Prisma.Decimal; totalAmount: Prisma.Decimal }> {
    // Guard against negative values after discount
    const afterDiscount = subtotal.sub(orderDiscount);
    const safeAfterDiscount = afterDiscount.gte(0) ? afterDiscount : new Prisma.Decimal(0);

    let taxAmount: Prisma.Decimal;
    let totalAmount: Prisma.Decimal;

    if (options.auto) {
      const taxResult = await this.taxCalculationService.calculateWithDiscount(
        subtotal,
        orderDiscount,
        {
          taxType: options.taxOptions?.taxType || TaxType.PPN,
          isInclusive: options.inclusive,
          exemptItems: options.taxOptions?.exemptItems || [],
        }
      );
      taxAmount = new Prisma.Decimal(taxResult.taxAmount);
    } else {
      // Validate manual tax amount is provided when auto is false
      if (options.manualTaxAmount === undefined || options.manualTaxAmount === null) {
        throw new Error('Jumlah pajak manual harus diisi ketika kalkulasi otomatis dinonaktifkan');
      }
      taxAmount = new Prisma.Decimal(options.manualTaxAmount);
    }

    // Round tax amount to 2 decimal places
    taxAmount = taxAmount.toDecimalPlaces(2);

    // For inclusive tax, the total is the amount after discount (tax is already included)
    // For exclusive tax, add tax to the amount after discount
    if (options.inclusive) {
      totalAmount = safeAfterDiscount;
    } else {
      totalAmount = safeAfterDiscount.add(taxAmount);
    }

    // Round total amount to 2 decimal places
    totalAmount = totalAmount.toDecimalPlaces(2);

    return { taxAmount, totalAmount };
  }

  async create(createPurchaseOrderDto: CreatePurchaseOrderDto, userId: string) {
    // Validate supplier exists
    const supplier = await this.prisma.supplier.findUnique({
      where: { id: createPurchaseOrderDto.supplierId },
    });

    if (!supplier) {
      throw new BadRequestException('Supplier tidak ditemukan');
    }

    // Generate order number if not provided
    const orderNumber = createPurchaseOrderDto.orderNumber ||
      await this.numberGeneratorService.generatePurchaseOrderNumber();

    // Check if order number already exists
    const existingOrder = await this.prisma.purchaseOrder.findUnique({
      where: { orderNumber },
    });

    if (existingOrder) {
      throw new ConflictException('Nomor purchase order sudah digunakan');
    }

    // Validate and prepare items
    const { items, ...orderData } = createPurchaseOrderDto;

    if (!items || items.length === 0) {
      throw new BadRequestException('Purchase order harus memiliki minimal satu item');
    }

    // Validate products and units exist (batch queries to avoid N+1)
    const productIds = [...new Set(items.map(item => item.productId))];
    const unitIds = [...new Set(items.map(item => item.unitId))];

    const [products, units] = await Promise.all([
      this.prisma.product.findMany({
        where: { id: { in: productIds } },
        select: { id: true },
      }),
      this.prisma.productUnit.findMany({
        where: { id: { in: unitIds } },
        select: { id: true },
      }),
    ]);

    const foundProductIds = new Set(products.map(p => p.id));
    const foundUnitIds = new Set(units.map(u => u.id));

    // Check for missing products
    for (const productId of productIds) {
      if (!foundProductIds.has(productId)) {
        throw new BadRequestException(`Produk dengan ID ${productId} tidak ditemukan`);
      }
    }

    // Check for missing units
    for (const unitId of unitIds) {
      if (!foundUnitIds.has(unitId)) {
        throw new BadRequestException(`Unit dengan ID ${unitId} tidak ditemukan`);
      }
    }

    try {
      const purchaseOrder = await this.prisma.$transaction(async (prisma) => {
        // Calculate totals
        let subtotal = new Prisma.Decimal(0);
        const processedItems = items.map(item => {
          const itemTotal = new Prisma.Decimal(item.unitPrice).mul(item.quantityOrdered);
          let discountAmount = new Prisma.Decimal(0);

          if (item.discountType && item.discountValue) {
            if (item.discountType === 'PERCENTAGE') {
              discountAmount = itemTotal.mul(item.discountValue).div(100);
            } else if (item.discountType === 'FIXED_AMOUNT') {
              discountAmount = new Prisma.Decimal(item.discountValue);
            }
          }

          const finalItemTotal = itemTotal.sub(discountAmount);
          subtotal = subtotal.add(finalItemTotal);

          return {
            ...item,
            totalPrice: finalItemTotal,
            discountAmount,
            unitPrice: new Prisma.Decimal(item.unitPrice),
          };
        });

        // Apply order-level discount
        let orderDiscountAmount = new Prisma.Decimal(0);
        if (orderData.discountType && orderData.discountValue) {
          if (orderData.discountType === 'PERCENTAGE') {
            orderDiscountAmount = subtotal.mul(orderData.discountValue).div(100);
          } else if (orderData.discountType === 'FIXED_AMOUNT') {
            orderDiscountAmount = new Prisma.Decimal(orderData.discountValue);
          }
        }

        // Calculate tax and total amount using helper method
        const { taxAmount, totalAmount } = await this.computeTax(
          subtotal,
          orderDiscountAmount,
          {
            auto: orderData.autoCalculateTax !== false, // Default to auto-calculate
            inclusive: orderData.taxInclusive || false,
            taxOptions: orderData.taxOptions,
            manualTaxAmount: orderData.taxAmount,
          }
        );

        // Create purchase order
        const order = await prisma.purchaseOrder.create({
          data: {
            orderNumber,
            supplierId: createPurchaseOrderDto.supplierId,
            status: createPurchaseOrderDto.status || PurchaseOrderStatus.SUBMITTED,
            orderDate: createPurchaseOrderDto.orderDate ? new Date(createPurchaseOrderDto.orderDate) : new Date(),
            expectedDelivery: createPurchaseOrderDto.expectedDelivery ? new Date(createPurchaseOrderDto.expectedDelivery) : null,
            subtotal,
            discountType: orderData.discountType,
            discountValue: orderData.discountValue ? new Prisma.Decimal(orderData.discountValue) : null,
            discountAmount: orderDiscountAmount,
            taxAmount,
            totalAmount,
            paymentTerms: orderData.paymentTerms,
            paymentMethod: orderData.paymentMethod,
            deliveryAddress: orderData.deliveryAddress,
            deliveryContact: orderData.deliveryContact,
            deliveryPhone: orderData.deliveryPhone,
            deliveryNotes: orderData.deliveryNotes,
            notes: orderData.notes,
            internalNotes: orderData.internalNotes,
            createdBy: userId,
            updatedBy: userId,
          },
        });

        // Create purchase order items
        await prisma.purchaseOrderItem.createMany({
          data: processedItems.map(item => ({
            purchaseOrderId: order.id,
            productId: item.productId,
            unitId: item.unitId,
            quantityOrdered: item.quantityOrdered,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice,
            discountType: item.discountType,
            discountValue: item.discountValue ? new Prisma.Decimal(item.discountValue) : null,
            discountAmount: item.discountAmount,
            expectedDelivery: item.expectedDelivery ? new Date(item.expectedDelivery) : null,
            notes: item.notes,
          })),
        });

        return order;
      });

      return this.findOne(purchaseOrder.id);
    } catch (error) {
      this.logger.error({
        err: error,
        orderNumber,
        supplierId: createPurchaseOrderDto.supplierId,
        userId
      }, 'Failed to create purchase order');

      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException('Nomor purchase order sudah digunakan');
        }
      }
      throw error;
    }
  }

  async findAll(query: PurchaseOrderQueryDto) {
    const {
      page = 1,
      limit = 10,
      search,
      supplierId,
      status,
      orderDateFrom,
      orderDateTo,
      expectedDeliveryFrom,
      expectedDeliveryTo,
      createdBy,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = query;

    const skip = (page - 1) * limit;

    // Build where clause
    const where: Prisma.PurchaseOrderWhereInput = {};

    if (search) {
      where.OR = [
        { orderNumber: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } },
        { supplier: { name: { contains: search, mode: 'insensitive' } } },
      ];
    }

    if (supplierId) {
      where.supplierId = supplierId;
    }

    if (status) {
      where.status = status as PurchaseOrderStatus;
    }

    if (orderDateFrom || orderDateTo) {
      where.orderDate = {};
      if (orderDateFrom) {
        where.orderDate.gte = new Date(orderDateFrom);
      }
      if (orderDateTo) {
        where.orderDate.lte = new Date(orderDateTo);
      }
    }

    if (expectedDeliveryFrom || expectedDeliveryTo) {
      where.expectedDelivery = {};
      if (expectedDeliveryFrom) {
        where.expectedDelivery.gte = new Date(expectedDeliveryFrom);
      }
      if (expectedDeliveryTo) {
        where.expectedDelivery.lte = new Date(expectedDeliveryTo);
      }
    }

    if (createdBy) {
      where.createdBy = createdBy;
    }

    // Build order by
    const orderBy: Prisma.PurchaseOrderOrderByWithRelationInput = {};
    if (sortBy === 'supplier') {
      orderBy.supplier = { name: sortOrder };
    } else if (sortBy === 'totalAmount') {
      orderBy.totalAmount = sortOrder;
    } else {
      orderBy[sortBy as keyof Prisma.PurchaseOrderOrderByWithRelationInput] = sortOrder;
    }

    try {
      const [purchaseOrders, total] = await Promise.all([
        this.prisma.purchaseOrder.findMany({
          where,
          skip,
          take: limit,
          orderBy,
          include: {
            supplier: {
              select: {
                id: true,
                code: true,
                name: true,
                type: true,
                status: true,
              },
            },
            items: {
              include: {
                product: {
                  select: {
                    id: true,
                    code: true,
                    name: true,
                    type: true,
                  },
                },
                unit: {
                  select: {
                    id: true,
                    name: true,
                    abbreviation: true,
                  },
                },
              },
            },
            createdByUser: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },

            _count: {
              select: {
                items: true,
                goodsReceipts: true,
              },
            },
          },
        }),
        this.prisma.purchaseOrder.count({ where }),
      ]);

      return {
        data: purchaseOrders,
        meta: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
          hasNextPage: page < Math.ceil(total / limit),
          hasPreviousPage: page > 1,
        },
      };
    } catch (error) {
      this.logger.error({ err: error, query }, 'Failed to retrieve purchase orders');
      throw error;
    }
  }

  async findOne(id: string) {
    try {
      const purchaseOrder = await this.prisma.purchaseOrder.findUnique({
        where: { id },
        include: {
          supplier: {
            include: {
              contacts: {
                where: { isPrimary: true },
                take: 1,
              },
            },
          },
          items: {
            include: {
              product: {
                include: {
                  baseUnit: true,
                },
              },
              unit: true,
              goodsReceiptItems: {
                include: {
                  goodsReceipt: {
                    select: {
                      id: true,
                      receiptNumber: true,
                      status: true,
                      receiptDate: true,
                    },
                  },
                },
              },
            },
            orderBy: { createdAt: 'asc' },
          },
          goodsReceipts: {
            select: {
              id: true,
              receiptNumber: true,
              status: true,
              receiptDate: true,

            },
            orderBy: { receiptDate: 'desc' },
          },
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
          updatedByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },

        },
      });

      if (!purchaseOrder) {
        throw new NotFoundException('Purchase order tidak ditemukan');
      }

      return purchaseOrder;
    } catch (error) {
      if (!(error instanceof NotFoundException)) {
        this.logger.error({ err: error, purchaseOrderId: id }, 'Failed to retrieve purchase order');
      }
      throw error;
    }
  }

  async update(id: string, updatePurchaseOrderDto: UpdatePurchaseOrderDto, userId: string) {
    const existingOrder = await this.findOne(id);

    // Check if order can be updated based on status
    if (existingOrder.status === PurchaseOrderStatus.COMPLETED ||
      existingOrder.status === PurchaseOrderStatus.CANCELLED) {
      throw new BadRequestException('Purchase order yang sudah selesai atau dibatalkan tidak dapat diubah');
    }

    try {
      const updatedOrder = await this.prisma.$transaction(async (prisma) => {
        // Prepare update data without items, supplierId, and non-schema fields
        const {
          items,
          supplierId,
          autoCalculateTax,
          taxInclusive,
          taxOptions,
          ...orderUpdateData
        } = updatePurchaseOrderDto;

        // Prepare the update data object with only valid schema fields
        const updateData: any = {
          // Only include fields that exist in the Prisma schema
          orderDate: orderUpdateData.orderDate ? new Date(orderUpdateData.orderDate) : undefined,
          expectedDelivery: orderUpdateData.expectedDelivery ? new Date(orderUpdateData.expectedDelivery) : undefined,
          discountType: orderUpdateData.discountType,
          discountValue: orderUpdateData.discountValue ? new Prisma.Decimal(orderUpdateData.discountValue) : undefined,
          taxAmount: orderUpdateData.taxAmount ? new Prisma.Decimal(orderUpdateData.taxAmount) : undefined,
          paymentTerms: orderUpdateData.paymentTerms,
          paymentMethod: orderUpdateData.paymentMethod,
          deliveryAddress: orderUpdateData.deliveryAddress,
          deliveryContact: orderUpdateData.deliveryContact,
          deliveryPhone: orderUpdateData.deliveryPhone,
          deliveryNotes: orderUpdateData.deliveryNotes,
          notes: orderUpdateData.notes,
          internalNotes: orderUpdateData.internalNotes,
          // Note: updatedBy and other audit fields are handled by Prisma automatically
        };

        // Remove undefined values to avoid Prisma issues
        Object.keys(updateData).forEach(key => {
          if (updateData[key] === undefined) {
            delete updateData[key];
          }
        });

        // Handle supplier update if provided
        if (supplierId) {
          updateData.supplier = {
            connect: { id: supplierId }
          };
        }

        // Update purchase order
        await prisma.purchaseOrder.update({
          where: { id },
          data: updateData,
        });

        // Handle items update if provided
        if (updatePurchaseOrderDto.items) {
          // Validate products and units exist (batch queries to avoid N+1)
          const productIds = [...new Set(updatePurchaseOrderDto.items.map(item => item.productId))];
          const unitIds = [...new Set(updatePurchaseOrderDto.items.map(item => item.unitId))];

          const [products, units] = await Promise.all([
            prisma.product.findMany({
              where: { id: { in: productIds } },
              select: { id: true },
            }),
            prisma.productUnit.findMany({
              where: { id: { in: unitIds } },
              select: { id: true },
            }),
          ]);

          const foundProductIds = new Set(products.map(p => p.id));
          const foundUnitIds = new Set(units.map(u => u.id));

          // Check for missing products
          for (const productId of productIds) {
            if (!foundProductIds.has(productId)) {
              throw new BadRequestException(`Produk dengan ID ${productId} tidak ditemukan`);
            }
          }

          // Check for missing units
          for (const unitId of unitIds) {
            if (!foundUnitIds.has(unitId)) {
              throw new BadRequestException(`Unit dengan ID ${unitId} tidak ditemukan`);
            }
          }

          // Get existing items
          const existingItems = await prisma.purchaseOrderItem.findMany({
            where: { purchaseOrderId: id },
          });

          // Create maps for efficient lookup
          const existingItemsMap = new Map(existingItems.map(item => [
            `${item.productId}-${item.unitId}`, item
          ]));

          const incomingItemsMap = new Map(updatePurchaseOrderDto.items.map(item => [
            `${item.productId}-${item.unitId}`, item
          ]));

          // Find items to delete (exist in DB but not in incoming)
          const itemsToDelete = existingItems.filter(existing =>
            !incomingItemsMap.has(`${existing.productId}-${existing.unitId}`)
          );

          // Find items to create (in incoming but not in DB)
          const itemsToCreate = updatePurchaseOrderDto.items.filter(incoming =>
            !existingItemsMap.has(`${incoming.productId}-${incoming.unitId}`)
          );

          // Find items to update (exist in both)
          const itemsToUpdate = updatePurchaseOrderDto.items.filter(incoming =>
            existingItemsMap.has(`${incoming.productId}-${incoming.unitId}`)
          );

          // Delete items that are no longer needed
          if (itemsToDelete.length > 0) {
            await prisma.purchaseOrderItem.deleteMany({
              where: {
                id: { in: itemsToDelete.map(item => item.id) },
              },
            });
          }

          // Process items with proper discount calculations (following create method pattern)
          let subtotal = new Prisma.Decimal(0);
          const processedItems = updatePurchaseOrderDto.items.map(item => {
            const itemTotal = new Prisma.Decimal(item.unitPrice).mul(item.quantityOrdered);
            let discountAmount = new Prisma.Decimal(0);

            if (item.discountType && item.discountValue) {
              if (item.discountType === 'PERCENTAGE') {
                discountAmount = itemTotal.mul(item.discountValue).div(100);
              } else if (item.discountType === 'FIXED_AMOUNT') {
                discountAmount = new Prisma.Decimal(item.discountValue);
              }
            }

            const finalItemTotal = itemTotal.sub(discountAmount);
            subtotal = subtotal.add(finalItemTotal);

            return {
              ...item,
              totalPrice: finalItemTotal,
              discountAmount,
              unitPrice: new Prisma.Decimal(item.unitPrice),
            };
          });

          // Create new items with calculated values
          if (itemsToCreate.length > 0) {
            const itemsToCreateData = itemsToCreate.map(item => {
              const processedItem = processedItems.find(p =>
                p.productId === item.productId && p.unitId === item.unitId
              );

              if (!processedItem) {
                throw new BadRequestException(`Processed item not found for product ${item.productId} and unit ${item.unitId}`);
              }

              return {
                purchaseOrderId: id,
                productId: item.productId,
                unitId: item.unitId,
                quantityOrdered: item.quantityOrdered,
                unitPrice: processedItem.unitPrice,
                totalPrice: processedItem.totalPrice,
                discountType: item.discountType,
                discountValue: item.discountValue ? new Prisma.Decimal(item.discountValue) : null,
                discountAmount: processedItem.discountAmount,
                expectedDelivery: item.expectedDelivery ? new Date(item.expectedDelivery) : null,

                notes: item.notes,
              };
            });

            await prisma.purchaseOrderItem.createMany({
              data: itemsToCreateData,
            });
          }

          // Update existing items with calculated values
          for (const incomingItem of itemsToUpdate) {
            const existingItem = existingItemsMap.get(`${incomingItem.productId}-${incomingItem.unitId}`);
            const processedItem = processedItems.find(p =>
              p.productId === incomingItem.productId && p.unitId === incomingItem.unitId
            );

            if (!existingItem) {
              throw new BadRequestException(`Existing item not found for product ${incomingItem.productId} and unit ${incomingItem.unitId}`);
            }

            if (!processedItem) {
              throw new BadRequestException(`Processed item not found for product ${incomingItem.productId} and unit ${incomingItem.unitId}`);
            }

            await prisma.purchaseOrderItem.update({
              where: { id: existingItem.id },
              data: {
                quantityOrdered: incomingItem.quantityOrdered,
                unitPrice: processedItem.unitPrice,
                totalPrice: processedItem.totalPrice,
                discountType: incomingItem.discountType,
                discountValue: incomingItem.discountValue ? new Prisma.Decimal(incomingItem.discountValue) : null,
                discountAmount: processedItem.discountAmount,
                expectedDelivery: incomingItem.expectedDelivery ? new Date(incomingItem.expectedDelivery) : null,

                notes: incomingItem.notes,
              },
            });
          }

          // Apply order-level discount (following create method pattern)
          let orderDiscountAmount = new Prisma.Decimal(0);
          if (updatePurchaseOrderDto.discountType && updatePurchaseOrderDto.discountValue) {
            if (updatePurchaseOrderDto.discountType === 'PERCENTAGE') {
              orderDiscountAmount = subtotal.mul(updatePurchaseOrderDto.discountValue).div(100);
            } else if (updatePurchaseOrderDto.discountType === 'FIXED_AMOUNT') {
              orderDiscountAmount = new Prisma.Decimal(updatePurchaseOrderDto.discountValue);
            }
          }

          // Calculate tax and total amount using helper method (same as create method)
          const { taxAmount, totalAmount } = await this.computeTax(
            subtotal,
            orderDiscountAmount,
            {
              auto: autoCalculateTax !== false, // Default to auto-calculate
              inclusive: taxInclusive || false,
              taxOptions: taxOptions,
              manualTaxAmount: updatePurchaseOrderDto.taxAmount,
            }
          );

          // Update purchase order with calculated totals
          await prisma.purchaseOrder.update({
            where: { id },
            data: {
              subtotal,
              discountAmount: orderDiscountAmount,
              taxAmount,
              totalAmount,
            },
          });
        } else {
          // If no items are being updated, still recalculate totals if discount or tax settings changed
          const shouldRecalculate =
            updatePurchaseOrderDto.discountType !== undefined ||
            updatePurchaseOrderDto.discountValue !== undefined ||
            autoCalculateTax !== undefined ||
            taxInclusive !== undefined ||
            taxOptions !== undefined ||
            updatePurchaseOrderDto.taxAmount !== undefined;

          if (shouldRecalculate) {
            // Get current items to recalculate totals
            const currentItems = await prisma.purchaseOrderItem.findMany({
              where: { purchaseOrderId: id },
            });

            // Calculate subtotal from current items (item totals already have item-level discounts applied)
            const subtotal = currentItems.reduce((sum, item) =>
              sum.add(item.totalPrice), new Prisma.Decimal(0));

            // Apply order-level discount
            let orderDiscountAmount = new Prisma.Decimal(0);
            if (updatePurchaseOrderDto.discountType && updatePurchaseOrderDto.discountValue) {
              if (updatePurchaseOrderDto.discountType === 'PERCENTAGE') {
                orderDiscountAmount = subtotal.mul(updatePurchaseOrderDto.discountValue).div(100);
              } else if (updatePurchaseOrderDto.discountType === 'FIXED_AMOUNT') {
                orderDiscountAmount = new Prisma.Decimal(updatePurchaseOrderDto.discountValue);
              }
            }

            // Calculate tax and total amount using helper method
            const { taxAmount, totalAmount } = await this.computeTax(
              subtotal,
              orderDiscountAmount,
              {
                auto: autoCalculateTax !== false, // Default to auto-calculate
                inclusive: taxInclusive || false,
                taxOptions: taxOptions,
                manualTaxAmount: updatePurchaseOrderDto.taxAmount,
              }
            );

            // Update purchase order with recalculated totals
            await prisma.purchaseOrder.update({
              where: { id },
              data: {
                subtotal,
                discountAmount: orderDiscountAmount,
                taxAmount,
                totalAmount,
              },
            });
          }
        }

        return prisma.purchaseOrder.findUnique({
          where: { id },
          include: {
            supplier: true,
            items: {
              include: {
                product: true,
                unit: true,
              },
            },
          },
        });
      });

      return updatedOrder;
    } catch (error) {
      // Enhanced error handling for purchase order updates
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2002':
            throw new ConflictException('Nomor purchase order sudah digunakan');
          case 'P2025':
            throw new BadRequestException('Purchase order tidak ditemukan');
          case 'P2003':
            throw new BadRequestException('Referensi data tidak valid (supplier, produk, atau unit tidak ditemukan)');
          default:
            this.logger.error(`Prisma error during purchase order update: ${error.code} - ${error.message}`);
            throw new BadRequestException('Gagal memperbarui purchase order');
        }
      }

      if (error instanceof Prisma.PrismaClientValidationError) {
        this.logger.error(`Prisma validation error during purchase order update: ${error.message}`);
        throw new BadRequestException('Data tidak valid untuk memperbarui purchase order');
      }

      if (error instanceof BadRequestException) {
        throw error; // Re-throw our custom validation errors
      }

      // Log unexpected errors
      this.logger.error(`Unexpected error during purchase order update: ${error.message}`, error.stack);
      throw new BadRequestException('Terjadi kesalahan saat memperbarui purchase order');
    }
  }



  async updateStatus(id: string, statusDto: PurchaseOrderStatusUpdateDto, userId: string) {
    const existingOrder = await this.findOne(id);

    // Validate status transition
    if (!ProcurementValidationUtils.validatePurchaseOrderStatusTransition(
      existingOrder.status,
      statusDto.status
    )) {
      throw new BadRequestException(
        `Tidak dapat mengubah status dari ${existingOrder.status} ke ${statusDto.status}`
      );
    }

    const updateData: any = {
      status: statusDto.status,
      updatedBy: userId,
    };

    // Add specific fields based on status
    if (statusDto.status === PurchaseOrderStatus.ORDERED) {
      updateData.sentAt = new Date();
    }

    if (statusDto.notes) {
      updateData.notes = statusDto.notes;
    }

    const updatedOrder = await this.prisma.purchaseOrder.update({
      where: { id },
      data: updateData,
    });

    return this.findOne(updatedOrder.id);
  }

  async cancel(id: string, reason: string, userId: string) {
    const existingOrder = await this.findOne(id);

    // Validate status transition
    if (!ProcurementValidationUtils.validatePurchaseOrderStatusTransition(
      existingOrder.status,
      PurchaseOrderStatus.CANCELLED
    )) {
      throw new BadRequestException(
        `Tidak dapat membatalkan purchase order dengan status ${existingOrder.status}`
      );
    }

    const updatedOrder = await this.prisma.purchaseOrder.update({
      where: { id },
      data: {
        status: PurchaseOrderStatus.CANCELLED,
        internalNotes: `Dibatalkan: ${reason}`,
        updatedBy: userId,
      },
    });

    return this.findOne(updatedOrder.id);
  }

  async sendToSupplier(id: string, userId: string) {
    this.logger.trace({ purchaseOrderId: id, userId }, 'Entering sendToSupplier method');

    const existingOrder = await this.findOne(id);

    // Validate status transition - can only send if status is SUBMITTED
    if (!ProcurementValidationUtils.validatePurchaseOrderStatusTransition(
      existingOrder.status,
      PurchaseOrderStatus.ORDERED
    )) {
      this.logger.warn({
        purchaseOrderId: id,
        currentStatus: existingOrder.status,
        targetStatus: PurchaseOrderStatus.ORDERED,
        userId
      }, 'Invalid status transition for sendToSupplier');

      throw new BadRequestException(
        `Tidak dapat mengirim purchase order dengan status ${existingOrder.status}. Status harus SUBMITTED.`
      );
    }

    this.logger.debug({
      purchaseOrderId: id,
      orderNumber: existingOrder.orderNumber,
      supplierId: existingOrder.supplierId,
      itemCount: existingOrder.items.length,
      userId
    }, 'Starting purchase order send to supplier process');

    try {
      const result = await this.prisma.$transaction(async (prisma) => {
        // Update purchase order status to ORDERED
        const updatedOrder = await prisma.purchaseOrder.update({
          where: { id },
          data: {
            status: PurchaseOrderStatus.ORDERED,
            updatedBy: userId,
            // Add timestamp for when it was sent
            internalNotes: existingOrder.internalNotes
              ? `${existingOrder.internalNotes}\nDikirim ke supplier pada: ${new Date().toLocaleString('id-ID')}`
              : `Dikirim ke supplier pada: ${new Date().toLocaleString('id-ID')}`,
          },
        });

        this.logger.debug({
          purchaseOrderId: id,
          orderNumber: existingOrder.orderNumber,
          newStatus: PurchaseOrderStatus.ORDERED,
          userId
        }, 'Purchase order status updated to ORDERED');

        // Generate goods receipt number
        const receiptNumber = await this.numberGeneratorService.generateGoodsReceiptNumber();

        this.logger.debug({
          purchaseOrderId: id,
          receiptNumber,
          userId
        }, 'Generated goods receipt number');

        // Create goods receipt with PENDING status
        const goodsReceipt = await prisma.goodsReceipt.create({
          data: {
            receiptNumber,
            purchaseOrderId: id,
            supplierId: existingOrder.supplierId,
            status: GoodsReceiptStatus.PENDING,
            receiptDate: new Date(),
            createdBy: userId,
            updatedBy: userId,
            totalAmount: existingOrder.totalAmount,
            notes: `Goods receipt dibuat otomatis saat purchase order dikirim ke supplier`,
          },
        });

        this.logger.debug({
          purchaseOrderId: id,
          goodsReceiptId: goodsReceipt.id,
          receiptNumber: goodsReceipt.receiptNumber,
          userId
        }, 'Goods receipt created successfully');

        // Create goods receipt items based on purchase order items
        const goodsReceiptItems = existingOrder.items.map(item => ({
          goodsReceiptId: goodsReceipt.id,
          purchaseOrderItemId: item.id,
          productId: item.productId,
          unitId: item.unitId,
          quantityOrdered: item.quantityOrdered,
          quantityReceived: item.quantityOrdered, // Initially set to ordered quantity
          quantityAccepted: 0, // Will be set during quality control
          quantityRejected: 0, // Will be set during quality control
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          notes: `Item dari purchase order ${existingOrder.orderNumber}`,
        }));

        await prisma.goodsReceiptItem.createMany({
          data: goodsReceiptItems,
        });

        this.logger.info({
          purchaseOrderId: id,
          orderNumber: existingOrder.orderNumber,
          goodsReceiptId: goodsReceipt.id,
          receiptNumber: goodsReceipt.receiptNumber,
          itemCount: goodsReceiptItems.length,
          totalAmount: existingOrder.totalAmount,
          userId
        }, 'Purchase order sent to supplier and goods receipt created successfully');

        return updatedOrder;
      });

      return this.findOne(result.id);
    } catch (error) {
      this.logger.error({
        err: error,
        purchaseOrderId: id,
        orderNumber: existingOrder.orderNumber,
        supplierId: existingOrder.supplierId,
        userId
      }, 'Failed to send purchase order to supplier and create goods receipt');

      // Re-throw the error to maintain existing error handling behavior
      throw error;
    }
  }

  async remove(id: string) {
    const existingOrder = await this.findOne(id);

    // Only allow deletion of draft orders
    if (existingOrder.status !== PurchaseOrderStatus.DRAFT) {
      throw new BadRequestException('Hanya purchase order dengan status DRAFT yang dapat dihapus');
    }

    await this.prisma.$transaction(async (prisma) => {
      // Delete items first
      await prisma.purchaseOrderItem.deleteMany({
        where: { purchaseOrderId: id },
      });

      // Delete purchase order
      await prisma.purchaseOrder.delete({
        where: { id },
      });
    });
  }

  async getStats(period?: string) {
    // Calculate date range based on period
    const now = new Date();
    let dateFilter: { gte?: Date; lte?: Date } | undefined;

    if (period && period !== 'all') {
      const startDate = new Date();
      switch (period) {
        case '7d':
          startDate.setDate(now.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(now.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(now.getDate() - 90);
          break;
        case 'quarter':
          startDate.setMonth(now.getMonth() - 3);
          break;
        case 'year':
          startDate.setFullYear(now.getFullYear() - 1);
          break;
      }
      dateFilter = { gte: startDate, lte: now };
    }

    const whereClause = dateFilter ? { createdAt: dateFilter } : {};

    const [
      totalOrders,
      statusCounts,
      totalValue,
      recentOrders,
      valueByStatus,
      supplierStats,
      processingTimeStats,
      volumeTrends
    ] = await Promise.all([
      this.prisma.purchaseOrder.count({ where: whereClause }),
      this.prisma.purchaseOrder.groupBy({
        by: ['status'],
        where: whereClause,
        _count: { status: true },
      }),
      this.prisma.purchaseOrder.aggregate({
        where: whereClause,
        _sum: { totalAmount: true },
      }),
      this.prisma.purchaseOrder.findMany({
        where: whereClause,
        take: 5,
        orderBy: { createdAt: 'desc' },
        include: {
          supplier: {
            select: { name: true },
          },
        },
      }),
      // Value breakdown by status
      this.prisma.purchaseOrder.groupBy({
        by: ['status'],
        where: whereClause,
        _sum: { totalAmount: true },
      }),
      // Top suppliers by order count and value
      this.prisma.purchaseOrder.groupBy({
        by: ['supplierId'],
        where: whereClause,
        _count: { supplierId: true },
        _sum: { totalAmount: true },
        orderBy: { _count: { supplierId: 'desc' } },
        take: 5,
      }),
      // Processing time analytics (time from creation to completion)
      this.prisma.purchaseOrder.findMany({
        where: {
          ...whereClause,
          status: PurchaseOrderStatus.COMPLETED,
        },
        select: {
          createdAt: true,
          updatedAt: true,
        },
      }),
      // Volume trends (daily counts for the last 30 days)
      this.prisma.$queryRaw<Array<{ date: Date; count: bigint }>>`
        SELECT
          DATE("purchase_orders"."createdAt") as date,
          COUNT(*) as count
        FROM public."purchase_orders"
        WHERE "purchase_orders"."createdAt" >= NOW() - INTERVAL '30 days'
        GROUP BY DATE("purchase_orders"."createdAt")
        ORDER BY date DESC
        LIMIT 30
      `,
    ]);

    // Process status statistics
    const statusStats = statusCounts.reduce((acc, item) => {
      acc[item.status] = item._count.status;
      return acc;
    }, {} as Record<string, number>);

    // Process value breakdown by status
    const valueStats = valueByStatus.reduce((acc, item) => {
      acc[item.status] = Number(item._sum.totalAmount || 0);
      return acc;
    }, {} as Record<string, number>);

    // Calculate derived metrics
    const pendingValue = (valueStats.DRAFT || 0) + (valueStats.SUBMITTED || 0);
    const activeValue = (valueStats.ORDERED || 0) + (valueStats.PARTIALLY_RECEIVED || 0);
    const completedValue = valueStats.COMPLETED || 0;

    // Process supplier statistics
    const supplierStatsWithNames = await Promise.all(
      supplierStats.map(async (stat) => {
        const supplier = await this.prisma.supplier.findUnique({
          where: { id: stat.supplierId },
          select: { name: true },
        });
        return {
          supplierId: stat.supplierId,
          supplierName: supplier?.name || 'Unknown Supplier',
          orderCount: stat._count.supplierId,
          totalValue: Number(stat._sum.totalAmount || 0),
        };
      })
    );

    // Calculate processing time analytics
    const processingTimes = processingTimeStats.map(order => {
      return Math.abs(new Date(order.updatedAt).getTime() - new Date(order.createdAt).getTime()) / (1000 * 60 * 60); // hours
    }).filter(time => time > 0);

    const averageProcessingTime = processingTimes.length > 0
      ? processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length
      : 0;

    const fastProcessing = processingTimes.filter(time => time <= 24).length;
    const slowProcessing = processingTimes.filter(time => time > 72).length;
    const averageProcessing = processingTimes.filter(time => time > 24 && time <= 72).length;

    const processingTimeAnalytics = {
      averageProcessingTime,
      totalOrders: processingTimes.length,
      efficiencyMetrics: {
        fastProcessing,
        slowProcessing,
        averageProcessing,
        fastPercentage: processingTimes.length > 0 ? (fastProcessing / processingTimes.length) * 100 : 0,
        slowPercentage: processingTimes.length > 0 ? (slowProcessing / processingTimes.length) * 100 : 0,
      },
    };

    // Process volume trends
    const processedVolumeTrends = volumeTrends.map(trend => ({
      date: trend.date,
      count: Number(trend.count),
    }));

    return {
      totalOrders,
      statusStats,
      totalValue: Number(totalValue._sum.totalAmount || 0),
      pendingValue,
      activeValue,
      completedValue,
      valueStats,
      recentOrders,
      supplierStats: supplierStatsWithNames,
      processingTimeAnalytics,
      volumeTrends: processedVolumeTrends,
      period: period || 'all',
    };
  }
}

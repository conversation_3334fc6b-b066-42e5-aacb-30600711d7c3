import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { Providers as SessionProviders } from "./providers";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/sonner";
import { NavigationProgressProvider } from "@/components/providers/navigation-progress-provider";
import { GlobalLoadingBar } from "@/components/ui/loading-bar";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Apotek App",
  description: "Sistem Manajemen Apotek - Indonesian Pharmacy Management System",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased overflow-x-hidden`}
      >
        <SessionProviders>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            storageKey="apotek-theme"
            disableTransitionOnChange
          >
            <NavigationProgressProvider
              config={{
                minimumLoadingTime: 300,
                showDelay: 100,
                hideDelay: 400,
                showForSamePage: false,
                maxCompilationTime: 10000,
                enableRouterInterception: true,
                detectCompilation: true,
              }}
            >
              <GlobalLoadingBar />
              {children}
              <Toaster />
            </NavigationProgressProvider>
          </ThemeProvider>
        </SessionProviders>
      </body>
    </html>
  );
}

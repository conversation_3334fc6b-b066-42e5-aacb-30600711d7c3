'use client';

import { useContext } from 'react';
import { NavigationProgressContext } from '@/components/providers/navigation-progress-provider';
import { NavigationProgressContextValue } from '@/types/navigation';

/**
 * Hook to access navigation progress state and controls
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const { isLoading, progress, startLoading, completeLoading } = useNavigationProgress();
 *   
 *   const handleCustomNavigation = async () => {
 *     startLoading();
 *     try {
 *       await someAsyncOperation();
 *       completeLoading();
 *     } catch (error) {
 *       completeLoading();
 *     }
 *   };
 *   
 *   return (
 *     <div>
 *       {isLoading && <p>Memuat...</p>}
 *       <button onClick={handleCustomNavigation}>
 *         Navigasi Kustom
 *       </button>
 *     </div>
 *   );
 * }
 * ```
 */
export function useNavigationProgress(): NavigationProgressContextValue {
  const context = useContext(NavigationProgressContext);
  
  if (!context) {
    throw new Error(
      'useNavigationProgress must be used within a NavigationProgressProvider. ' +
      'Make sure to wrap your app with NavigationProgressProvider.'
    );
  }
  
  return context;
}

/**
 * Hook for manual loading control with automatic cleanup
 * Useful for custom async operations that should show loading state
 * 
 * @example
 * ```tsx
 * function DataFetcher() {
 *   const { withLoading } = useLoadingControl();
 *   
 *   const fetchData = withLoading(async () => {
 *     const response = await fetch('/api/data');
 *     return response.json();
 *   });
 *   
 *   return <button onClick={fetchData}>Muat Data</button>;
 * }
 * ```
 */
export function useLoadingControl() {
  const { startLoading, completeLoading, setProgress } = useNavigationProgress();
  
  /**
   * Wraps an async function with loading state management
   */
  const withLoading = <T extends (...args: any[]) => Promise<any>>(
    asyncFn: T,
    options?: {
      /** Custom progress updates during execution */
      onProgress?: (progress: number) => void;
      /** Minimum loading time in ms */
      minimumTime?: number;
    }
  ): T => {
    return (async (...args: Parameters<T>) => {
      const startTime = Date.now();
      const { onProgress, minimumTime = 300 } = options || {};
      
      try {
        startLoading();
        
        // Set initial progress
        setProgress(10);
        
        // Execute the async function
        const result = await asyncFn(...args);
        
        // Ensure minimum loading time for better UX
        const elapsed = Date.now() - startTime;
        if (elapsed < minimumTime) {
          await new Promise(resolve => setTimeout(resolve, minimumTime - elapsed));
        }
        
        // Complete loading
        setProgress(100);
        completeLoading();
        
        return result;
      } catch (error) {
        // Always complete loading on error
        completeLoading();
        throw error;
      }
    }) as T;
  };
  
  /**
   * Manual progress control for complex operations
   */
  const withProgressControl = async <T>(
    operation: (updateProgress: (progress: number) => void) => Promise<T>
  ): Promise<T> => {
    try {
      startLoading();
      setProgress(0);
      
      const result = await operation((progress) => {
        setProgress(Math.min(Math.max(progress, 0), 95)); // Cap at 95% until complete
      });
      
      setProgress(100);
      completeLoading();
      
      return result;
    } catch (error) {
      completeLoading();
      throw error;
    }
  };
  
  return {
    withLoading,
    withProgressControl,
    startLoading,
    completeLoading,
    setProgress,
  };
}

/**
 * Hook for checking loading state only (read-only)
 * Useful for components that only need to display loading state
 */
export function useLoadingState() {
  const { isLoading, progress, isVisible } = useNavigationProgress();
  
  return {
    isLoading,
    progress,
    isVisible,
  };
}

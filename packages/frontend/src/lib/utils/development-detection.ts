'use client';

/**
 * Utilities for detecting development mode and Next.js compilation states
 */

/**
 * Detects if the application is running in development mode
 */
export function isDevelopmentMode(): boolean {
  return process.env.NODE_ENV === 'development';
}

/**
 * Detects if the application is running in production mode
 */
export function isProductionMode(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * Detects if Next.js is likely in turbo mode (faster compilation)
 */
export function isTurboMode(): boolean {
  // Check for turbo-specific environment variables or indicators
  return typeof window !== 'undefined' && 
         (window as any).__NEXT_DATA__?.buildId?.includes('turbo') ||
         process.env.NEXT_PRIVATE_TURBO === 'true';
}

/**
 * Gets the estimated compilation time based on environment
 */
export function getEstimatedCompilationTime(): number {
  if (isProductionMode()) {
    return 0; // No compilation in production
  }
  
  if (isTurboMode()) {
    return 1000; // Turbo mode is faster
  }
  
  return 3000; // Standard development compilation
}

/**
 * Detects if we're in a hot reload scenario
 */
export function isHotReloadActive(): boolean {
  if (typeof window === 'undefined') return false;
  
  // Check for Next.js hot reload indicators
  return !!(window as any).__NEXT_DATA__?.page ||
         !!(window as any).__webpack_require__ ||
         !!(window as any).webpackHotUpdate;
}

/**
 * Monitors for Next.js compilation indicators
 */
export class CompilationMonitor {
  private isMonitoring = false;
  private compilationCallbacks: Array<(isCompiling: boolean) => void> = [];
  private lastNavigationTime = 0;
  private navigationTimeoutId: NodeJS.Timeout | null = null;
  
  constructor(
    private compilationTimeoutMs: number = 2000,
    private maxCompilationTime: number = 10000
  ) {}

  /**
   * Start monitoring for compilation delays
   */
  start(): void {
    if (this.isMonitoring || !isDevelopmentMode()) return;
    
    this.isMonitoring = true;
    this.setupNavigationMonitoring();
  }

  /**
   * Stop monitoring
   */
  stop(): void {
    this.isMonitoring = false;
    this.cleanup();
  }

  /**
   * Add callback for compilation state changes
   */
  onCompilationChange(callback: (isCompiling: boolean) => void): () => void {
    this.compilationCallbacks.push(callback);
    
    // Return cleanup function
    return () => {
      const index = this.compilationCallbacks.indexOf(callback);
      if (index > -1) {
        this.compilationCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Manually trigger compilation detection
   */
  detectCompilation(): void {
    if (!this.isMonitoring) return;
    
    this.lastNavigationTime = Date.now();
    this.notifyCompilationStart();
    
    // Set timeout to detect if compilation is taking too long
    this.navigationTimeoutId = setTimeout(() => {
      this.notifyCompilationEnd();
    }, this.compilationTimeoutMs);
  }

  /**
   * Manually end compilation detection
   */
  endCompilation(): void {
    if (this.navigationTimeoutId) {
      clearTimeout(this.navigationTimeoutId);
      this.navigationTimeoutId = null;
    }
    this.notifyCompilationEnd();
  }

  private setupNavigationMonitoring(): void {
    if (typeof window === 'undefined') return;

    // Monitor for performance navigation timing
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          for (const entry of entries) {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming;
              
              // If navigation takes longer than expected, it's likely compilation
              const loadTime = navEntry.loadEventEnd - navEntry.fetchStart;
              if (loadTime > this.compilationTimeoutMs) {
                this.detectCompilation();
              }
            }
          }
        });
        
        observer.observe({ entryTypes: ['navigation'] });
      } catch (error) {
        console.warn('Performance monitoring not available:', error);
      }
    }

    // Monitor for document readiness changes
    const handleReadyStateChange = () => {
      if (document.readyState === 'loading' && this.lastNavigationTime > 0) {
        const elapsed = Date.now() - this.lastNavigationTime;
        if (elapsed > this.compilationTimeoutMs) {
          this.detectCompilation();
        }
      } else if (document.readyState === 'complete') {
        this.endCompilation();
      }
    };

    document.addEventListener('readystatechange', handleReadyStateChange);
  }

  private notifyCompilationStart(): void {
    this.compilationCallbacks.forEach(callback => {
      try {
        callback(true);
      } catch (error) {
        console.error('Error in compilation callback:', error);
      }
    });
  }

  private notifyCompilationEnd(): void {
    this.compilationCallbacks.forEach(callback => {
      try {
        callback(false);
      } catch (error) {
        console.error('Error in compilation callback:', error);
      }
    });
  }

  private cleanup(): void {
    if (this.navigationTimeoutId) {
      clearTimeout(this.navigationTimeoutId);
      this.navigationTimeoutId = null;
    }
  }
}

/**
 * Global compilation monitor instance
 */
let globalCompilationMonitor: CompilationMonitor | null = null;

/**
 * Get or create the global compilation monitor
 */
export function getCompilationMonitor(): CompilationMonitor {
  if (!globalCompilationMonitor) {
    globalCompilationMonitor = new CompilationMonitor();
  }
  return globalCompilationMonitor;
}

/**
 * Cleanup global compilation monitor
 */
export function cleanupCompilationMonitor(): void {
  if (globalCompilationMonitor) {
    globalCompilationMonitor.stop();
    globalCompilationMonitor = null;
  }
}

/**
 * Simple utility to detect if a navigation is likely waiting for compilation
 */
export function isLikelyCompiling(navigationStartTime: number, timeoutMs: number = 1500): boolean {
  if (!isDevelopmentMode()) return false;
  
  const elapsed = Date.now() - navigationStartTime;
  return elapsed > timeoutMs;
}

'use client';

import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';
import { RouterInterceptor } from '@/types/navigation';

/**
 * Creates a router interceptor that can capture navigation attempts
 * before they reach Next.js router, allowing us to start loading
 * immediately before compilation begins.
 */
export function createRouterInterceptor(
  router: AppRouterInstance,
  onNavigationStart: (url: string, method: 'push' | 'replace') => void
): RouterInterceptor {
  let isActive = false;
  let originalPush: typeof router.push;
  let originalReplace: typeof router.replace;

  const start = () => {
    if (isActive) return;
    
    // Store original methods
    originalPush = router.push.bind(router);
    originalReplace = router.replace.bind(router);
    
    // Intercept push method
    router.push = (href: string, options?: any) => {
      onNavigationStart(href, 'push');
      return originalPush(href, options);
    };
    
    // Intercept replace method
    router.replace = (href: string, options?: any) => {
      onNavigationStart(href, 'replace');
      return originalReplace(href, options);
    };
    
    isActive = true;
  };

  const stop = () => {
    if (!isActive) return;
    
    // Restore original methods
    if (originalPush) {
      router.push = originalPush;
    }
    if (originalReplace) {
      router.replace = originalReplace;
    }
    
    isActive = false;
  };

  return {
    originalPush: originalPush!,
    originalReplace: originalReplace!,
    isActive,
    start,
    stop,
  };
}

/**
 * Detects if we're in development mode where compilation delays occur
 */
export function isDevelopmentMode(): boolean {
  return process.env.NODE_ENV === 'development';
}

/**
 * Detects if a URL change represents a meaningful navigation
 * that should trigger loading state
 */
export function shouldShowLoadingForNavigation(
  currentUrl: string,
  targetUrl: string,
  showForSamePage: boolean = false
): boolean {
  if (typeof window === 'undefined') return false;
  
  try {
    const current = new URL(currentUrl, window.location.origin);
    const target = new URL(targetUrl, window.location.origin);
    
    // Different pathname always shows loading
    if (current.pathname !== target.pathname) {
      return true;
    }
    
    // Same pathname but different search params
    if (current.search !== target.search) {
      return showForSamePage;
    }
    
    // Same pathname and search, different hash
    if (current.hash !== target.hash) {
      return false; // Hash changes don't typically require loading
    }
    
    return false;
  } catch (error) {
    // If URL parsing fails, assume we should show loading
    return true;
  }
}

/**
 * Gets the current URL for comparison
 */
export function getCurrentUrl(): string {
  if (typeof window === 'undefined') return '';
  return window.location.pathname + window.location.search;
}

/**
 * Normalizes a URL for consistent comparison
 */
export function normalizeUrl(url: string): string {
  if (typeof window === 'undefined') return url;
  
  try {
    const normalized = new URL(url, window.location.origin);
    return normalized.pathname + normalized.search;
  } catch (error) {
    return url;
  }
}

/**
 * Creates a compilation detector that monitors for Next.js compilation delays
 */
export function createCompilationDetector(
  onCompilationStart: () => void,
  onCompilationEnd: () => void,
  maxCompilationTime: number = 10000 // 10 seconds max
) {
  let isCompiling = false;
  let compilationStartTime: number | null = null;
  let compilationTimer: NodeJS.Timeout | null = null;
  let performanceObserver: PerformanceObserver | null = null;

  const start = () => {
    if (!isDevelopmentMode()) return;
    
    // Monitor for long navigation delays which indicate compilation
    if ('PerformanceObserver' in window) {
      performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        for (const entry of entries) {
          if (entry.entryType === 'navigation' && entry.duration > 1000) {
            // Long navigation detected, likely compilation
            if (!isCompiling) {
              isCompiling = true;
              compilationStartTime = Date.now();
              onCompilationStart();
              
              // Set maximum compilation time
              compilationTimer = setTimeout(() => {
                if (isCompiling) {
                  reset();
                  onCompilationEnd();
                }
              }, maxCompilationTime);
            }
          }
        }
      });
      
      performanceObserver.observe({ entryTypes: ['navigation'] });
    }
  };

  const stop = () => {
    if (performanceObserver) {
      performanceObserver.disconnect();
      performanceObserver = null;
    }
    
    if (compilationTimer) {
      clearTimeout(compilationTimer);
      compilationTimer = null;
    }
  };

  const reset = () => {
    isCompiling = false;
    compilationStartTime = null;
    
    if (compilationTimer) {
      clearTimeout(compilationTimer);
      compilationTimer = null;
    }
  };

  return {
    get isCompiling() { return isCompiling; },
    get compilationStartTime() { return compilationStartTime; },
    start,
    stop,
    reset,
  };
}

/**
 * Detects if the current navigation is likely waiting for compilation
 * by monitoring for delayed route changes
 */
export function createNavigationTimeoutDetector(
  onTimeout: () => void,
  timeoutMs: number = 2000
) {
  let timeoutId: NodeJS.Timeout | null = null;
  
  const start = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = setTimeout(() => {
      onTimeout();
    }, timeoutMs);
  };
  
  const cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };
  
  return {
    start,
    cancel,
  };
}

/**
 * Navigation progress types for global loading indicator
 */

export interface NavigationProgressState {
  /** Whether navigation is currently in progress */
  isLoading: boolean;
  /** Current progress percentage (0-100) */
  progress: number;
  /** Whether the loading bar should be visible */
  isVisible: boolean;
}

export interface NavigationProgressContextValue extends NavigationProgressState {
  /** Start loading manually */
  startLoading: () => void;
  /** Complete loading manually */
  completeLoading: () => void;
  /** Set specific progress value */
  setProgress: (progress: number) => void;
  /** Reset loading state */
  reset: () => void;
}

export interface NavigationProgressProviderProps {
  children: React.ReactNode;
  /** Custom configuration for the progress behavior */
  config?: NavigationProgressConfig;
}

export interface NavigationProgressConfig {
  /** Minimum time to show loading bar (ms) */
  minimumLoadingTime?: number;
  /** Animation duration for progress updates (ms) */
  animationDuration?: number;
  /** Delay before showing loading bar (ms) */
  showDelay?: number;
  /** Delay before hiding loading bar after completion (ms) */
  hideDelay?: number;
  /** Whether to show loading for same-page navigation */
  showForSamePage?: boolean;
}

export interface LoadingBarProps {
  /** Current progress percentage (0-100) */
  progress: number;
  /** Whether the loading bar is visible */
  isVisible: boolean;
  /** Custom className for styling */
  className?: string;
  /** Height of the loading bar in pixels */
  height?: number;
  /** Color theme for the loading bar */
  color?: 'primary' | 'secondary' | 'accent';
}

export type NavigationEvent = 'start' | 'complete' | 'error';

export interface NavigationEventHandler {
  (event: NavigationEvent, url?: string): void;
}

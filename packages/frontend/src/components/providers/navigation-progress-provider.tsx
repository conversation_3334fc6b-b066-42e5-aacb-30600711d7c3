'use client';

import React, { createContext, useCallback, useEffect, useRef, useState } from 'react';
import { usePathname, useSearchParams, useRouter } from 'next/navigation';
import {
  NavigationProgressContextValue,
  NavigationProgressProviderProps,
  NavigationProgressConfig,
  NavigationProgressState,
} from '@/types/navigation';
import {
  createRouterInterceptor,
  normalizeUrl
} from '@/lib/utils/router-interceptor';
import {
  isDevelopmentMode,
  getCompilationMonitor,
  isLikelyCompiling
} from '@/lib/utils/development-detection';

// Default configuration
const DEFAULT_CONFIG: Required<NavigationProgressConfig> = {
  minimumLoadingTime: 300,
  animationDuration: 200,
  showDelay: 100,
  hideDelay: 500,
  showForSamePage: false,
  maxCompilationTime: 10000,
  enableRouterInterception: true,
  detectCompilation: true,
};

// Create context
export const NavigationProgressContext = createContext<NavigationProgressContextValue | null>(null);

/**
 * Provider that tracks Next.js App Router navigation and manages global loading state
 * 
 * Features:
 * - Automatic detection of route changes
 * - Smooth progress animations
 * - Configurable timing and behavior
 * - Manual loading control
 * - SSR-safe implementation
 */
export function NavigationProgressProvider({
  children,
  config: userConfig,
}: NavigationProgressProviderProps) {
  const config = { ...DEFAULT_CONFIG, ...userConfig };

  // Navigation tracking
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const previousPath = useRef<string>('');
  const previousSearch = useRef<string>('');

  // Router interception and compilation detection
  const routerInterceptor = useRef<ReturnType<typeof createRouterInterceptor> | null>(null);
  const compilationMonitor = useRef<ReturnType<typeof getCompilationMonitor> | null>(null);
  const navigationStartTime = useRef<number>(0);
  const pendingNavigation = useRef<string | null>(null);
  
  // Loading state
  const [state, setState] = useState<NavigationProgressState>({
    isLoading: false,
    progress: 0,
    isVisible: false,
  });
  
  // Timers for managing animations and delays
  const showTimer = useRef<NodeJS.Timeout | undefined>(undefined);
  const hideTimer = useRef<NodeJS.Timeout | undefined>(undefined);
  const progressTimer = useRef<NodeJS.Timeout | undefined>(undefined);
  const minimumTimeTimer = useRef<NodeJS.Timeout | undefined>(undefined);
  
  // Track loading start time for minimum loading time
  const loadingStartTime = useRef<number>(0);
  
  // Cleanup timers
  const clearTimers = useCallback(() => {
    if (showTimer.current) clearTimeout(showTimer.current);
    if (hideTimer.current) clearTimeout(hideTimer.current);
    if (progressTimer.current) clearTimeout(progressTimer.current);
    if (minimumTimeTimer.current) clearTimeout(minimumTimeTimer.current);
  }, []);

  // Start loading with smooth progress animation
  const startLoading = useCallback(() => {
    clearTimers();
    loadingStartTime.current = Date.now();

    // Show loading bar with minimal delay in development mode
    const delay = isDevelopmentMode() ? 0 : config.showDelay;

    showTimer.current = setTimeout(() => {
      setState(prev => ({ ...prev, isVisible: true, isLoading: true, progress: 0 }));

      // Animate progress to 30% quickly, then slow down
      let currentProgress = 0;
      const animateProgress = () => {
        currentProgress += currentProgress < 30 ? 10 : 2;
        if (currentProgress <= 90) {
          setState(prev => ({ ...prev, progress: currentProgress }));
          progressTimer.current = setTimeout(animateProgress, config.animationDuration);
        }
      };

      animateProgress();
    }, delay);
  }, [clearTimers, config.showDelay, config.animationDuration]);
  
  // Complete loading with proper timing
  const completeLoading = useCallback(() => {
    clearTimers();
    
    const finishLoading = () => {
      setState(prev => ({ ...prev, progress: 100 }));
      
      // Hide after completion delay
      hideTimer.current = setTimeout(() => {
        setState(prev => ({ ...prev, isLoading: false, isVisible: false, progress: 0 }));
      }, config.hideDelay);
    };
    
    // Ensure minimum loading time
    const elapsed = Date.now() - loadingStartTime.current;
    if (elapsed < config.minimumLoadingTime) {
      minimumTimeTimer.current = setTimeout(finishLoading, config.minimumLoadingTime - elapsed);
    } else {
      finishLoading();
    }
  }, [clearTimers, config.hideDelay, config.minimumLoadingTime]);
  
  // Set specific progress value
  const setProgress = useCallback((progress: number) => {
    setState(prev => ({ ...prev, progress: Math.min(Math.max(progress, 0), 100) }));
  }, []);
  
  // Reset loading state
  const reset = useCallback(() => {
    clearTimers();
    setState({ isLoading: false, progress: 0, isVisible: false });
  }, [clearTimers]);

  // Handle navigation start (called by router interceptor)
  const handleNavigationStart = useCallback((url: string, _method: 'push' | 'replace') => {
    navigationStartTime.current = Date.now();
    pendingNavigation.current = url;

    // Start loading immediately for development mode or always if configured
    if (isDevelopmentMode() || config.enableRouterInterception) {
      startLoading();

      // In development mode, set up compilation timeout detection
      if (isDevelopmentMode() && config.detectCompilation) {
        const compilationTimeout = setTimeout(() => {
          // If navigation is still pending after timeout, likely compilation is happening
          if (pendingNavigation.current === url && isLikelyCompiling(navigationStartTime.current)) {
            // Trigger compilation detection
            compilationMonitor.current?.detectCompilation();
          }
        }, 1500); // 1.5 seconds timeout

        // Cleanup timeout when navigation completes
        setTimeout(() => {
          if (pendingNavigation.current !== url) {
            clearTimeout(compilationTimeout);
          }
        }, config.maxCompilationTime);
      }
    }
  }, [config.enableRouterInterception, config.detectCompilation, config.maxCompilationTime, startLoading]);
  
  // Setup router interception and compilation monitoring
  useEffect(() => {
    if (!config.enableRouterInterception) return;

    // Setup router interceptor
    routerInterceptor.current = createRouterInterceptor(router, handleNavigationStart);
    routerInterceptor.current.start();

    // Setup compilation monitoring in development mode
    if (config.detectCompilation && isDevelopmentMode()) {
      compilationMonitor.current = getCompilationMonitor();

      const cleanupCompilationCallback = compilationMonitor.current.onCompilationChange((isCompiling) => {
        if (isCompiling) {
          // Compilation started - ensure loading is active
          if (!state.isLoading) {
            startLoading();
          }
        } else {
          // Compilation ended - complete loading
          completeLoading();
        }
      });

      compilationMonitor.current.start();

      return () => {
        cleanupCompilationCallback();
        compilationMonitor.current?.stop();
      };
    }

    return () => {
      routerInterceptor.current?.stop();
    };
  }, [config.enableRouterInterception, config.detectCompilation, handleNavigationStart, startLoading, completeLoading, state.isLoading]);

  // Track navigation changes (fallback for when interception is disabled)
  useEffect(() => {
    const currentPath = pathname;
    const currentSearch = searchParams.toString();
    const fullCurrentPath = `${currentPath}${currentSearch ? `?${currentSearch}` : ''}`;
    const fullPreviousPath = `${previousPath.current}${previousSearch.current ? `?${previousSearch.current}` : ''}`;

    // Check if this is a meaningful navigation change
    const isNavigationChange = previousPath.current && fullCurrentPath !== fullPreviousPath;
    const isSamePageNavigation = currentPath === previousPath.current && currentSearch !== previousSearch.current;

    if (isNavigationChange) {
      // Clear pending navigation if we've reached the destination
      if (pendingNavigation.current && normalizeUrl(pendingNavigation.current) === fullCurrentPath) {
        pendingNavigation.current = null;
        completeLoading();
      } else if (!config.enableRouterInterception) {
        // Fallback behavior when interception is disabled
        if (isSamePageNavigation && !config.showForSamePage) {
          reset();
        } else {
          startLoading();
          const completeTimer = setTimeout(() => {
            completeLoading();
          }, 100);
          return () => clearTimeout(completeTimer);
        }
      }
    }

    // Update previous path tracking
    previousPath.current = currentPath;
    previousSearch.current = currentSearch;
  }, [pathname, searchParams, startLoading, completeLoading, reset, config.showForSamePage, config.enableRouterInterception]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearTimers();
    };
  }, [clearTimers]);
  
  const contextValue: NavigationProgressContextValue = {
    ...state,
    startLoading,
    completeLoading,
    setProgress,
    reset,
  };
  
  return (
    <NavigationProgressContext.Provider value={contextValue}>
      {children}
    </NavigationProgressContext.Provider>
  );
}

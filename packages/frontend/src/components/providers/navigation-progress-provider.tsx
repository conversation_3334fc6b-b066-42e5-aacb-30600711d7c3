'use client';

import React, { createContext, useCallback, useEffect, useRef, useState } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import {
  NavigationProgressContextValue,
  NavigationProgressProviderProps,
  NavigationProgressConfig,
  NavigationProgressState,
} from '@/types/navigation';

// Default configuration
const DEFAULT_CONFIG: Required<NavigationProgressConfig> = {
  minimumLoadingTime: 300,
  animationDuration: 200,
  showDelay: 100,
  hideDelay: 500,
  showForSamePage: false,
};

// Create context
export const NavigationProgressContext = createContext<NavigationProgressContextValue | null>(null);

/**
 * Provider that tracks Next.js App Router navigation and manages global loading state
 * 
 * Features:
 * - Automatic detection of route changes
 * - Smooth progress animations
 * - Configurable timing and behavior
 * - Manual loading control
 * - SSR-safe implementation
 */
export function NavigationProgressProvider({
  children,
  config: userConfig,
}: NavigationProgressProviderProps) {
  const config = { ...DEFAULT_CONFIG, ...userConfig };
  
  // Navigation tracking
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const previousPath = useRef<string>('');
  const previousSearch = useRef<string>('');
  
  // Loading state
  const [state, setState] = useState<NavigationProgressState>({
    isLoading: false,
    progress: 0,
    isVisible: false,
  });
  
  // Timers for managing animations and delays
  const showTimer = useRef<NodeJS.Timeout | undefined>(undefined);
  const hideTimer = useRef<NodeJS.Timeout | undefined>(undefined);
  const progressTimer = useRef<NodeJS.Timeout | undefined>(undefined);
  const minimumTimeTimer = useRef<NodeJS.Timeout | undefined>(undefined);
  
  // Track loading start time for minimum loading time
  const loadingStartTime = useRef<number>(0);
  
  // Cleanup timers
  const clearTimers = useCallback(() => {
    if (showTimer.current) clearTimeout(showTimer.current);
    if (hideTimer.current) clearTimeout(hideTimer.current);
    if (progressTimer.current) clearTimeout(progressTimer.current);
    if (minimumTimeTimer.current) clearTimeout(minimumTimeTimer.current);
  }, []);
  
  // Start loading with smooth progress animation
  const startLoading = useCallback(() => {
    clearTimers();
    loadingStartTime.current = Date.now();
    
    // Show loading bar after delay
    showTimer.current = setTimeout(() => {
      setState(prev => ({ ...prev, isVisible: true, isLoading: true, progress: 0 }));
      
      // Animate progress to 30% quickly, then slow down
      let currentProgress = 0;
      const animateProgress = () => {
        currentProgress += currentProgress < 30 ? 10 : 2;
        if (currentProgress <= 90) {
          setState(prev => ({ ...prev, progress: currentProgress }));
          progressTimer.current = setTimeout(animateProgress, config.animationDuration);
        }
      };
      
      animateProgress();
    }, config.showDelay);
  }, [clearTimers, config.showDelay, config.animationDuration]);
  
  // Complete loading with proper timing
  const completeLoading = useCallback(() => {
    clearTimers();
    
    const finishLoading = () => {
      setState(prev => ({ ...prev, progress: 100 }));
      
      // Hide after completion delay
      hideTimer.current = setTimeout(() => {
        setState(prev => ({ ...prev, isLoading: false, isVisible: false, progress: 0 }));
      }, config.hideDelay);
    };
    
    // Ensure minimum loading time
    const elapsed = Date.now() - loadingStartTime.current;
    if (elapsed < config.minimumLoadingTime) {
      minimumTimeTimer.current = setTimeout(finishLoading, config.minimumLoadingTime - elapsed);
    } else {
      finishLoading();
    }
  }, [clearTimers, config.hideDelay, config.minimumLoadingTime]);
  
  // Set specific progress value
  const setProgress = useCallback((progress: number) => {
    setState(prev => ({ ...prev, progress: Math.min(Math.max(progress, 0), 100) }));
  }, []);
  
  // Reset loading state
  const reset = useCallback(() => {
    clearTimers();
    setState({ isLoading: false, progress: 0, isVisible: false });
  }, [clearTimers]);
  
  // Track navigation changes
  useEffect(() => {
    const currentPath = pathname;
    const currentSearch = searchParams.toString();
    const fullCurrentPath = `${currentPath}${currentSearch ? `?${currentSearch}` : ''}`;
    const fullPreviousPath = `${previousPath.current}${previousSearch.current ? `?${previousSearch.current}` : ''}`;
    
    // Check if this is a meaningful navigation change
    const isNavigationChange = previousPath.current && fullCurrentPath !== fullPreviousPath;
    const isSamePageNavigation = currentPath === previousPath.current && currentSearch !== previousSearch.current;
    
    if (isNavigationChange) {
      if (isSamePageNavigation && !config.showForSamePage) {
        // Same page navigation (query params only) - don't show loading unless configured
        reset();
      } else {
        // Different page navigation - show loading
        startLoading();
        
        // Complete loading after a short delay to simulate page load
        const completeTimer = setTimeout(() => {
          completeLoading();
        }, 100);
        
        return () => clearTimeout(completeTimer);
      }
    }
    
    // Update previous path tracking
    previousPath.current = currentPath;
    previousSearch.current = currentSearch;
  }, [pathname, searchParams, startLoading, completeLoading, reset, config.showForSamePage]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearTimers();
    };
  }, [clearTimers]);
  
  const contextValue: NavigationProgressContextValue = {
    ...state,
    startLoading,
    completeLoading,
    setProgress,
    reset,
  };
  
  return (
    <NavigationProgressContext.Provider value={contextValue}>
      {children}
    </NavigationProgressContext.Provider>
  );
}

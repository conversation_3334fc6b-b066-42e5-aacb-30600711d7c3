'use client';

import * as React from 'react';
import {
  ColumnDef,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { ChevronDown, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { PurchaseOrder, PurchaseOrderQueryParams, PurchaseOrderListResponse } from '@/types/purchase-order';
import { useDebounce, DEBOUNCE_DELAYS } from '@/lib/utils/debounce';
import { PURCHASE_ORDER_STATUS_OPTIONS, PAYMENT_METHOD_OPTIONS } from '@/lib/constants/purchase-order';

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  meta: PurchaseOrderListResponse['meta'];
  searchPlaceholder?: string;
  onRowClick?: (row: TData) => void;
  onQueryChange?: (query: PurchaseOrderQueryParams) => void;
  loading?: boolean;
  query: PurchaseOrderQueryParams;
  // Filter-related props
  filters: PurchaseOrderQueryParams;
  onFilterChange: (key: keyof PurchaseOrderQueryParams, value: any) => void;
  onClearFilters: () => void;
  filterOptions?: {
    suppliers?: { value: string; label: string }[];
  };
}

export function DataTable<TData, TValue>({
  columns,
  data,
  meta,
  searchPlaceholder = 'Cari purchase order...',
  onRowClick,
  onQueryChange,
  loading = false,
  query,
  filters,
  onFilterChange,
  onClearFilters,
  filterOptions,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({});
  const [searchValue, setSearchValue] = React.useState(query.search || '');

  // Debounced search using centralized utility
  const debouncedSearch = useDebounce(searchValue, DEBOUNCE_DELAYS.SEARCH);

  // Update query when debounced search changes
  React.useEffect(() => {
    if (onQueryChange) {
      onQueryChange({
        ...query,
        search: debouncedSearch || undefined,
        page: 1, // Reset to first page when searching
      });
    }
  }, [debouncedSearch]);

  // Update sorting when table sorting changes
  React.useEffect(() => {
    if (sorting.length > 0 && onQueryChange) {
      const sort = sorting[0];
      onQueryChange({
        ...query,
        sortBy: sort.id,
        sortOrder: sort.desc ? 'desc' : 'asc',
        page: 1, // Reset to first page when sorting
      });
    }
  }, [sorting]);

  const table = useReactTable({
    data,
    columns,
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnVisibility,
    },
    manualSorting: true,
    manualFiltering: true,
    manualPagination: true,
  });

  const handlePageChange = (newPage: number) => {
    if (onQueryChange) {
      onQueryChange({
        ...query,
        page: newPage,
      });
    }
  };

  const handlePageSizeChange = (newPageSize: string) => {
    if (onQueryChange) {
      onQueryChange({
        ...query,
        limit: parseInt(newPageSize),
        page: 1, // Reset to first page when changing page size
      });
    }
  };

  const hasActiveFilters = React.useMemo(() => {
    return !!(
      filters.status ||
      filters.supplierId ||
      filters.orderDateFrom ||
      filters.orderDateTo ||
      filters.expectedDeliveryFrom ||
      filters.expectedDeliveryTo
    );
  }, [filters]);

  return (
    <div className="w-full space-y-4">
      {/* Search and Filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-1 items-center space-x-2">
          <Input
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={(event) => setSearchValue(event.target.value)}
            className="h-8 w-[150px] lg:w-[250px]"
          />

          {/* Status Filter */}
          <Select
            value={filters.status || 'ALL'}
            onValueChange={(value) => onFilterChange('status', value === 'ALL' ? undefined : value)}
          >
            <SelectTrigger className="h-8 w-[120px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">Semua Status</SelectItem>
              {PURCHASE_ORDER_STATUS_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Supplier Filter */}
          {filterOptions?.suppliers && (
            <Select
              value={filters.supplierId || 'ALL'}
              onValueChange={(value) => onFilterChange('supplierId', value === 'ALL' ? undefined : value)}
            >
              <SelectTrigger className="h-8 w-[150px]">
                <SelectValue placeholder="Supplier" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Semua Supplier</SelectItem>
                {filterOptions.suppliers.map((supplier) => (
                  <SelectItem key={supplier.value} value={supplier.value}>
                    {supplier.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}

          {hasActiveFilters && (
            <Button
              variant="ghost"
              onClick={onClearFilters}
              className="h-8 px-2 lg:px-3"
            >
              Reset Filter
            </Button>
          )}
        </div>

        {/* Column Visibility */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="ml-auto h-8">
              <ChevronDown className="ml-2 h-4 w-4" />
              Kolom
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {loading ? (
              // Loading skeleton
              Array.from({ length: query.limit || 10 }).map((_, index) => (
                <TableRow key={index}>
                  {columns.map((_, colIndex) => (
                    <TableCell key={colIndex}>
                      <Skeleton className="h-4 w-full" />
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className={onRowClick ? 'cursor-pointer hover:bg-muted/50' : ''}
                  onClick={() => onRowClick?.(row.original)}
                >
                  {row.getVisibleCells().map((cell) => {
                    const isActionsColumn = cell.column.id === 'actions';
                    return (
                      <TableCell
                        key={cell.id}
                        onClick={(e) => {
                          if (isActionsColumn) {
                            e.stopPropagation();
                          }
                        }}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Tidak ada purchase order ditemukan.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between px-2">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">Baris per halaman</p>
          <Select
            value={`${query.limit || 10}`}
            onValueChange={handlePageSizeChange}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={query.limit || 10} />
            </SelectTrigger>
            <SelectContent side="top">
              {[10, 20, 30, 40, 50].map((pageSize) => (
                <SelectItem key={pageSize} value={`${pageSize}`}>
                  {pageSize}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex w-[100px] items-center justify-center text-sm font-medium">
            Halaman {meta.page} dari {meta.totalPages}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => handlePageChange(1)}
              disabled={!meta.hasPreviousPage || loading}
            >
              <span className="sr-only">Ke halaman pertama</span>
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => handlePageChange(meta.page - 1)}
              disabled={!meta.hasPreviousPage || loading}
            >
              <span className="sr-only">Ke halaman sebelumnya</span>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="h-8 w-8 p-0"
              onClick={() => handlePageChange(meta.page + 1)}
              disabled={!meta.hasNextPage || loading}
            >
              <span className="sr-only">Ke halaman selanjutnya</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              className="hidden h-8 w-8 p-0 lg:flex"
              onClick={() => handlePageChange(meta.totalPages)}
              disabled={!meta.hasNextPage || loading}
            >
              <span className="sr-only">Ke halaman terakhir</span>
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

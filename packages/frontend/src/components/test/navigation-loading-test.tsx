'use client';

import { useRouter } from 'next/navigation';
import { useNavigationProgress, useLoadingControl } from '@/hooks/useNavigationProgress';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

/**
 * Test component to verify the navigation loading indicator functionality
 * This component can be temporarily added to any page for testing
 */
export function NavigationLoadingTest() {
  const router = useRouter();
  const { isLoading, progress, isVisible, startLoading, completeLoading, setProgress } = useNavigationProgress();
  const { withLoading, withProgressControl } = useLoadingControl();

  // Test navigation to different pages
  const testNavigation = (path: string) => {
    router.push(path);
  };

  // Test manual loading control
  const testManualLoading = async () => {
    startLoading();
    await new Promise(resolve => setTimeout(resolve, 2000));
    completeLoading();
  };

  // Test loading with progress updates
  const testProgressLoading = withLoading(async () => {
    await new Promise(resolve => setTimeout(resolve, 1500));
  });

  // Test complex progress control
  const testComplexProgress = async () => {
    await withProgressControl(async (updateProgress) => {
      for (let i = 0; i <= 100; i += 10) {
        updateProgress(i);
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    });
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Test Indikator Loading Navigasi</CardTitle>
        <CardDescription>
          Komponen untuk menguji fungsionalitas loading bar global
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current State Display */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Status Saat Ini:</h3>
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Loading: </span>
              <span className={isLoading ? 'text-orange-600' : 'text-green-600'}>
                {isLoading ? 'Ya' : 'Tidak'}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">Visible: </span>
              <span className={isVisible ? 'text-orange-600' : 'text-green-600'}>
                {isVisible ? 'Ya' : 'Tidak'}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">Progress: </span>
              <span>{progress}%</span>
            </div>
          </div>
          <Progress value={progress} className="w-full" />
        </div>

        {/* Navigation Tests */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">Test Navigasi:</h3>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => testNavigation('/dashboard')}
            >
              Ke Dashboard
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => testNavigation('/dashboard/inventory')}
            >
              Ke Inventori
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => testNavigation('/dashboard/products')}
            >
              Ke Produk
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => testNavigation('/dashboard/suppliers')}
            >
              Ke Supplier
            </Button>
          </div>
          <div className="mt-2 p-2 bg-blue-50 rounded text-xs text-blue-700">
            💡 <strong>Development Mode:</strong> Loading bar akan muncul segera saat navigasi dimulai,
            bahkan sebelum kompilasi Next.js selesai. Coba navigasi ke halaman yang belum pernah dikunjungi
            untuk melihat efek kompilasi.
          </div>
        </div>

        {/* Manual Loading Tests */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">Test Loading Manual:</h3>
          <div className="grid grid-cols-2 gap-2">
            <Button 
              variant="secondary" 
              size="sm"
              onClick={testManualLoading}
              disabled={isLoading}
            >
              Loading Manual (2s)
            </Button>
            <Button 
              variant="secondary" 
              size="sm"
              onClick={testProgressLoading}
              disabled={isLoading}
            >
              Loading dengan Hook
            </Button>
          </div>
        </div>

        {/* Progress Control Tests */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">Test Kontrol Progress:</h3>
          <div className="grid grid-cols-3 gap-2">
            <Button 
              variant="secondary" 
              size="sm"
              onClick={() => setProgress(25)}
              disabled={isLoading}
            >
              Set 25%
            </Button>
            <Button 
              variant="secondary" 
              size="sm"
              onClick={() => setProgress(75)}
              disabled={isLoading}
            >
              Set 75%
            </Button>
            <Button 
              variant="secondary" 
              size="sm"
              onClick={testComplexProgress}
              disabled={isLoading}
            >
              Progress Bertahap
            </Button>
          </div>
        </div>

        {/* Manual Controls */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium">Kontrol Manual:</h3>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={startLoading}
              disabled={isLoading}
            >
              Mulai Loading
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              onClick={completeLoading}
              disabled={!isLoading}
            >
              Selesai Loading
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

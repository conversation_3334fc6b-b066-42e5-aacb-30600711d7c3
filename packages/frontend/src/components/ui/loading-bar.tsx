'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { LoadingBarProps } from '@/types/navigation';

/**
 * Global loading bar component that appears at the top of the page
 * during navigation and page loads. Uses the design system colors
 * and provides smooth animations.
 */
export function LoadingBar({
  progress,
  isVisible,
  className,
  height = 3,
  color = 'primary',
}: LoadingBarProps) {
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render on server to avoid hydration mismatch
  if (!mounted) {
    return null;
  }

  const colorClasses = {
    primary: 'bg-primary',
    secondary: 'bg-secondary',
    accent: 'bg-accent',
  };

  return (
    <div
      className={cn(
        'fixed top-0 left-0 right-0 z-[9999] transition-all duration-200 ease-out',
        isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none',
        className
      )}
      style={{ height: `${height}px` }}
      role="progressbar"
      aria-label="Memuat halaman"
      aria-valuenow={progress}
      aria-valuemin={0}
      aria-valuemax={100}
    >
      {/* Background track */}
      <div className="absolute inset-0 bg-border/20" />
      
      {/* Progress indicator */}
      <div
        className={cn(
          'absolute top-0 left-0 h-full transition-all duration-300 ease-out',
          colorClasses[color],
          // Add a subtle glow effect
          'shadow-sm',
          // Gradient effect for visual appeal
          'bg-gradient-to-r from-current to-current/80'
        )}
        style={{
          width: `${Math.min(Math.max(progress, 0), 100)}%`,
          transform: progress === 0 ? 'translateX(-100%)' : 'translateX(0)',
        }}
      />
      
      {/* Shimmer effect for active loading */}
      {isVisible && progress > 0 && progress < 100 && (
        <div
          className={cn(
            'absolute top-0 right-0 h-full w-20 opacity-60',
            'bg-gradient-to-r from-transparent via-white/30 to-transparent',
            'animate-pulse'
          )}
          style={{
            transform: `translateX(${progress < 90 ? '0' : '100%'})`,
            transition: 'transform 0.3s ease-out',
          }}
        />
      )}
    </div>
  );
}

/**
 * Simplified loading bar for specific use cases
 */
export function SimpleLoadingBar({
  isLoading,
  className,
}: {
  isLoading: boolean;
  className?: string;
}) {
  return (
    <LoadingBar
      progress={isLoading ? 30 : 100}
      isVisible={isLoading}
      className={className}
    />
  );
}

/**
 * Loading bar with indeterminate progress (continuous animation)
 */
export function IndeterminateLoadingBar({
  isVisible,
  className,
}: {
  isVisible: boolean;
  className?: string;
}) {
  return (
    <div
      className={cn(
        'fixed top-0 left-0 right-0 z-[9999] h-1 transition-opacity duration-200',
        isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none',
        className
      )}
    >
      <div className="absolute inset-0 bg-border/20" />
      <div
        className={cn(
          'absolute top-0 left-0 h-full w-1/3 bg-primary',
          'animate-[loading-bar_1.5s_ease-in-out_infinite]'
        )}
      />
      <style jsx>{`
        @keyframes loading-bar {
          0% {
            transform: translateX(-100%);
          }
          50% {
            transform: translateX(300%);
          }
          100% {
            transform: translateX(-100%);
          }
        }
      `}</style>
    </div>
  );
}

// Lazy load the content component outside of the component to avoid hook issues
const NavigationLoadingBarContent = React.lazy(() =>
  import('./navigation-loading-bar-content').then(module => ({
    default: module.NavigationLoadingBarContent
  }))
);

/**
 * Global navigation loading bar that automatically tracks navigation progress
 * This component should be placed at the root level of your application
 */
export function GlobalLoadingBar({ className }: { className?: string }) {
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render on server to avoid hydration issues
  if (!mounted) {
    return null;
  }

  return (
    <React.Suspense fallback={null}>
      <NavigationLoadingBarContent className={className} />
    </React.Suspense>
  );
}

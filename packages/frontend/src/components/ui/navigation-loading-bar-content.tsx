'use client';

import { useLoadingState } from '@/hooks/useNavigationProgress';
import { LoadingBar } from './loading-bar';

/**
 * Content component for the global navigation loading bar
 * Separated to avoid circular dependencies and enable lazy loading
 */
export function NavigationLoadingBarContent({ className }: { className?: string }) {
  const { progress, isVisible } = useLoadingState();
  
  return (
    <LoadingBar
      progress={progress}
      isVisible={isVisible}
      className={className}
      height={3}
      color="primary"
    />
  );
}

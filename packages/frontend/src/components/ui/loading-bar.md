# Global Page Loading Indicator

Sistem indikator loading global untuk aplikasi apotek yang menggunakan Next.js App Router. Menyediakan loading bar horizontal tipis di bagian atas halaman yang muncul saat navigasi dan loading halaman.

## Fitur

- ✅ **Otomatis**: Mendeteksi perubahan rute Next.js App Router secara otomatis
- ✅ **Kompilasi Development**: Menangani delay kompilasi Next.js di development mode
- ✅ **Router Interception**: Loading muncul segera saat navigasi dimulai (sebelum kompilasi)
- ✅ **Konfigurasi Fleksibel**: Dapat disesuaikan timing dan behavior
- ✅ **Kontrol Manual**: API untuk mengontrol loading state secara manual
- ✅ **TypeScript**: Fully typed dengan TypeScript
- ✅ **SSR-Safe**: Aman untuk server-side rendering
- ✅ **Performa Optimal**: Minimal re-renders dan optimized animations
- ✅ **Design System**: Menggunakan warna dan styling dari shadcn/ui
- ✅ **Development Optimized**: Khusus dioptimalkan untuk development workflow

## Komponen Utama

### 1. NavigationProgressProvider
Provider React Context yang mengelola state loading global.

### 2. GlobalLoadingBar
Komponen loading bar yang muncul di bagian atas halaman.

### 3. useNavigationProgress Hook
Hook untuk mengakses dan mengontrol loading state.

## Instalasi & Setup

Sistem ini sudah terintegrasi di root layout (`packages/frontend/src/app/layout.tsx`):

```tsx
import { NavigationProgressProvider } from "@/components/providers/navigation-progress-provider";
import { GlobalLoadingBar } from "@/components/ui/loading-bar";

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <NavigationProgressProvider>
          <GlobalLoadingBar />
          {children}
        </NavigationProgressProvider>
      </body>
    </html>
  );
}
```

## Penggunaan

### Otomatis (Default)
Loading bar akan muncul secara otomatis saat:
- Navigasi antar halaman
- Perubahan rute
- Loading halaman baru

### Manual Control
```tsx
import { useNavigationProgress } from '@/hooks/useNavigationProgress';

function MyComponent() {
  const { isLoading, progress, startLoading, completeLoading } = useNavigationProgress();
  
  const handleCustomAction = async () => {
    startLoading();
    try {
      await someAsyncOperation();
      completeLoading();
    } catch (error) {
      completeLoading();
    }
  };
  
  return (
    <div>
      {isLoading && <p>Sedang memuat...</p>}
      <button onClick={handleCustomAction}>
        Aksi Kustom
      </button>
    </div>
  );
}
```

### Loading dengan Progress Control
```tsx
import { useLoadingControl } from '@/hooks/useNavigationProgress';

function DataProcessor() {
  const { withProgressControl } = useLoadingControl();
  
  const processData = async () => {
    await withProgressControl(async (updateProgress) => {
      for (let i = 0; i <= 100; i += 10) {
        updateProgress(i);
        await processStep(i);
      }
    });
  };
  
  return <button onClick={processData}>Proses Data</button>;
}
```

### Loading dengan Hook Wrapper
```tsx
import { useLoadingControl } from '@/hooks/useNavigationProgress';

function ApiCaller() {
  const { withLoading } = useLoadingControl();
  
  const fetchData = withLoading(async () => {
    const response = await fetch('/api/data');
    return response.json();
  });
  
  return <button onClick={fetchData}>Ambil Data</button>;
}
```

## Konfigurasi

```tsx
<NavigationProgressProvider
  config={{
    minimumLoadingTime: 300,        // Waktu minimum loading (ms)
    showDelay: 100,                 // Delay sebelum menampilkan loading bar (ms)
    hideDelay: 400,                 // Delay sebelum menyembunyikan loading bar (ms)
    showForSamePage: false,         // Tampilkan untuk navigasi dalam halaman yang sama
    maxCompilationTime: 10000,      // Waktu maksimum kompilasi (ms)
    enableRouterInterception: true, // Aktifkan intersepsi router untuk loading instan
    detectCompilation: true,        // Deteksi delay kompilasi Next.js
  }}
>
  {children}
</NavigationProgressProvider>
```

### Konfigurasi Development vs Production

**Development Mode (Otomatis):**
- `enableRouterInterception: true` - Loading muncul segera saat navigasi dimulai
- `detectCompilation: true` - Mendeteksi dan menangani delay kompilasi Next.js
- `showDelay: 0` - Tidak ada delay untuk feedback instan

**Production Mode:**
- Kompilasi tidak terjadi, loading hanya untuk network requests
- Konfigurasi standar dengan delay normal

## API Reference

### useNavigationProgress()
```tsx
const {
  isLoading,        // boolean: Status loading saat ini
  progress,         // number: Progress 0-100
  isVisible,        // boolean: Apakah loading bar terlihat
  startLoading,     // function: Mulai loading
  completeLoading,  // function: Selesai loading
  setProgress,      // function: Set progress tertentu
  reset,           // function: Reset state loading
} = useNavigationProgress();
```

### useLoadingControl()
```tsx
const {
  withLoading,        // Wrapper untuk async function
  withProgressControl, // Control progress manual
  startLoading,       // Mulai loading
  completeLoading,    // Selesai loading
  setProgress,        // Set progress
} = useLoadingControl();
```

### useLoadingState() (Read-only)
```tsx
const {
  isLoading,  // boolean: Status loading
  progress,   // number: Progress saat ini
  isVisible,  // boolean: Visibility loading bar
} = useLoadingState();
```

## Testing

Untuk testing, gunakan komponen test yang tersedia:

```tsx
import { NavigationLoadingTest } from '@/components/test/navigation-loading-test';

// Tambahkan ke halaman untuk testing
<NavigationLoadingTest />
```

## Styling

Loading bar menggunakan design system yang ada:
- Warna: `primary` (default), `secondary`, `accent`
- Height: 3px (default)
- Posisi: Fixed di bagian atas halaman
- Z-index: 9999

## Best Practices

1. **Jangan override loading state** kecuali diperlukan
2. **Gunakan withLoading wrapper** untuk operasi async
3. **Selalu complete loading** di catch block
4. **Gunakan minimum loading time** untuk UX yang lebih baik
5. **Test dengan NavigationLoadingTest** sebelum deploy

## Troubleshooting

### Loading bar tidak muncul
- Pastikan NavigationProgressProvider membungkus aplikasi
- Periksa apakah GlobalLoadingBar sudah ditambahkan
- Cek console untuk error

### Loading tidak selesai
- Pastikan completeLoading() dipanggil di semua path
- Gunakan try-catch untuk error handling
- Periksa timer yang mungkin tidak ter-cleanup

### Hydration mismatch
- Komponen sudah SSR-safe dengan mounted state
- Jangan render loading bar di server side
